<template>
	<div :style="textStyle" 
		@click="handleClick" class="xtest">
	    {{ textContent }}
	</div>
</template>

<script lang="ts" setup>
	import { useAttrs, computed } from 'vue';
	import { useComponent } from '../../frame/useComponent.js'
	
	// 定义Props类型
	interface Props {
		DomItem?: {
			css?: Record<string, any>;
			value?: any;
			PageEvents?: {
				onClicked?: any;
			};
			[key: string]: any;
		};
	}
	
	const props = useAttrs() as Props;
	const { xRefCss, xOnClick, xRefValue } = useComponent(props);
	
	// 计算属性：文本样式
	const textStyle = computed(() => xRefCss(props.DomItem?.css));
	
	// 计算属性：文本内容
	const textContent = computed(() => xRefValue(props.DomItem?.value));
	
	// 点击处理函数
	const handleClick = () => {
		if (props.DomItem?.PageEvents?.onClicked) {
			xOnClick(props.DomItem.PageEvents.onClicked);
		}
	};
</script>

<script lang="ts">
export default {
  name: 'FWText'
}
</script>

<style lang="scss">
</style>