import { defineStore } from 'pinia';
import { reactive,toRaw } from 'vue';

export const useGlobalData = defineStore('globalData',()=>{
	
	let shareData = reactive({
		
	})
	
	let session = reactive({
		token:"fgdfgdfgd"
	})
	
	let app = reactive({
		appId:'55'
	})
	
	function setSession(token){
		session.token = token
		localStorage.setItem("session",JSON.stringify({type:'string',value:token}))
	}

	function getSession(){
		if(session.token.trim().length == 0){
			let sessionObj = localStorage.getItem("session")
			if(sessionObj){
				try {
					const parsedSession = JSON.parse(sessionObj)
					session.token = parsedSession.value
				} catch (e) {
					// 如果解析失败，清空 session
					clearSession()
				}
			}
		}
		return session.token
	}

	function clearSession(){
		session.token = ""
		localStorage.removeItem("session")
	}
	
	function setAppId(appId){
		app.appId = appId
	}
	
	function getAppId(){
		return app.appId
	}
	
	function setGlobalData(colname,data){
		shareData[colname] = data
	}
	
	function getGlobalData(colname){
		return shareData[colname]
	}
	
	return {setSession,getSession,clearSession,setGlobalData,getGlobalData,setAppId,getAppId}
})

export const usePageParam = defineStore('pageParam',()=>{
	
	let paramData = reactive({
		data:{},
		key:""
	})
	
	function setPageParam(param){
		paramData.data = param 
		paramData.key = "PM_"+new Date().getTime()
		return paramData.key
	}
	
	function getPageParam(key){
		if(paramData.key == key){
			return paramData.data
		}else{
			return null
		}
	}
	
	return {setPageParam,getPageParam}
})

export const useLocalStorage = ()=>{
	
	function setItem(key,value){
		let vObj = {type:typeof value,value:""}
		if(vObj.type =='object'){
			vObj.value = JSON.stringify(value)
		}else if(vObj.type=='string'){
			vObj.value = "" + value
		}else if(vObj.type=='boolean'){
			vObj.value = "" + value
		}else if(vObj.type=='number'){
			vObj.value = "" + value
		}
		localStorage.setItem(key,JSON.stringify(vObj))
	}
	
	function getItem(key){
		const vObjStr = localStorage.getItem(key)
		if(vObjStr && vObjStr.length > 0){
			const vObj = JSON.parse(vObjStr)
			if(vObj.hasOwnProperty('type') && vObj.type=='object'){
				return JSON.parse(vObj.value)
			}else if(vObj.hasOwnProperty('type') && vObj.type=='number'){
				return Number(vObj.value)
			}else if(vObj.hasOwnProperty('type') && vObj.type=='boolean'){
				return vObj.value == 'true'
			}else{
				return vObj.value
			}
		}
		return null
	}
	
	function removeItem(key){
		if(getItem(key) != null){
			localStorage.removeItem(key)
		}
	}
	
	return {setItem,getItem,removeItem}
}