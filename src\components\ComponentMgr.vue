<template>
	<!-- 基础组件 -->
	 <FWText        v-if="props.DomItem['type']=='TText' && xCheckVisible"      	  	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWButton     	v-else-if="props.DomItem['type']=='TButton' && xCheckVisible"     	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWImage      	v-else-if="props.DomItem['type']=='TImagePC' && xCheckVisible"    	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWInputVue   	v-else-if="props.DomItem['type']=='TInput' && xCheckVisible"      	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWArea      	v-else-if="props.DomItem['type']=='TArea' && xCheckVisible"       	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWEChatVue   	v-else-if="props.DomItem['type']=='echat' && xCheckVisible"       	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWLine       	v-else-if="props.DomItem['type']=='TLine' && xCheckVisible"       	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWRadio      	v-else-if="props.DomItem['type']=='TRadio' && xCheckVisible"      	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWCheckBox   	v-else-if="props.DomItem['type']=='TCheckBox' && xCheckVisible"   	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWIcon      	v-else-if="props.DomItem['type']=='TIcon' && xCheckVisible"       	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWSwitch    	v-else-if="props.DomItem['type']=='TSwitch' && xCheckVisible"     	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWSelect    	v-else-if="props.DomItem['type']=='TSelect' && xCheckVisible"     	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 
	 <!-- 复合组件 -->
	 <FWBox        	v-else-if="props.DomItem['type']=='TBox' && xCheckVisible"        	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWList       	v-else-if="props.DomItem['type']=='TListPC' && xCheckVisible"     	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWNavTreeVue 	v-else-if="props.DomItem['type']=='navTree' && xCheckVisible"     	:DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 <FWPartVue    	v-else-if="props.DomItem['type']=='TCustomCmpt' && xCheckVisible"   :DomItem="props.DomItem" :PathPrefix="props.PathPrefix" :ItemDataPath="props.ItemDataPath" />
	 
</template>

<script lang="ts" setup name="ComponentMgr">
	import { reactive,useAttrs,computed} from 'vue'
	import { useComponent } from '../frame/useComponent'
	import FWBox from './base/FWBox.vue'
	import FWButton from './base/FWButton.vue'
	import FWImage from './base/FWImage.vue'
	import FWText from './base/FWText.vue'
	import FWNavTreeVue from './base/FWNavTree.vue'
	import FWPartVue from './base/FWPart.vue'
	import FWInputVue from './base/FWInput.vue'
	import FWArea from './base/FWArea.vue'
	import FWEChatVue from './base/FWEChat.vue'
	import FWLine from './base/FWLine.vue'
	import FWRadio from './base/FWRadio.vue'
	import FWCheckBox from './base/FWCheckBox.vue'
	import FWIcon from './base/FWIcon.vue'
	import FWSwitch from './base/FWSwitch.vue'
	import FWSelect from './base/FWSelect.vue'
	import FWList from './base/FWList.vue'
	
	const props = useAttrs()
	const {xCalc} = useComponent(props)
	
	let xCheckVisible  = computed(()=>{
		if(!props.DomItem.hasOwnProperty('visible')) return true
		if(props.DomItem.hasOwnProperty('visible') ){
			let visi = xCalc(props.DomItem['visible'])
			if(typeof visi == 'boolean' && visi) return true
			if(typeof visi == 'string' && visi == 'true') return true
		}
		return false
	})
	
</script>

<script lang="ts">
export default {
  name: 'ComponentMgr'
}
</script>

<style lang="scss">
	
</style>