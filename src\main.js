import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import { createRouter, createWebHistory } from 'vue-router';
import routes from './router/router';
import { createPinia } from 'pinia';
import persistState from 'pinia-plugin-persistedstate';
import mitt  from 'mitt';

const app = createApp(App)
const router = createRouter({
  history: createWebHistory(), // 使用 HTML5 History API
  routes,
});
const pinia = createPinia();
app.use(router); // 全局注册路由
app.use(pinia);
app.use(ElementPlus,{ size: 'small', zIndex: 3000 })
app.config.globalProperties.emitter = mitt(); 

app.mount('#app')
