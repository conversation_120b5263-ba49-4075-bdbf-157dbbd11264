<template>
	<div ref="chartRef" :style="xRefCss(props.DomItem['css'])"></div>
</template>

<script lang="ts" setup>
	import { reactive,onMounted,useAttrs,ref } from 'vue';
	import {useComponent} from '../../frame/useComponent.js'
	import * as echarts from 'echarts';
	const props = useAttrs()
	const {xRefCss,xRefValue} = useComponent(props)
	const chartRef = ref(null);
	
	onMounted(()=>{
		let myChart = echarts.init(chartRef.value);
		  var option = {
		    title: props.DomItem.title,
		    tooltip: {},
		    xAxis: props.DomItem.xAxis,
		    yAxis: props.DomItem.yAxis,
		    series: props.DomItem.series
		  };
		  myChart.setOption(option);
	})
	
</script>

<script lang="ts">
export default {
  name: 'FWEChat'
}
</script>

<style lang="scss">
</style>