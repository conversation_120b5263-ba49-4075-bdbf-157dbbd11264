import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 系统通用函数库
 * 提供各种工具函数，包括数据类型检查、字符串处理、UI交互等功能
 */
export const xSysFunc = {
	/**
	 * 检查值是否为数字
	 * @param {any} value - 要检查的值
	 * @returns {boolean} 如果是数字返回true，否则返回false
	 * @example
	 * xSysFunc.isNumeric("123") // true
	 * xSysFunc.isNumeric("12.34") // true
	 * xSysFunc.isNumeric("abc") // false
	 */
	isNumeric(value){
		this.sumFuncTime("commonIsNumeric")
		// 使用正则表达式检查是否为有效数字格式（包括整数、小数、科学计数法）
		return /^[+-]?(?:\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$/.test(value);
	},

	/**
	 * 清除字符串两端的单引号
	 * @param {any} value - 要处理的值
	 * @returns {any} 如果是被单引号包围的字符串，返回去除引号后的内容；否则返回原值
	 * @example
	 * xSysFunc.clearAA("'hello'") // "hello"
	 * xSysFunc.clearAA("hello") // "hello"
	 * xSysFunc.clearAA(123) // 123
	 */
	clearAA(value){
		this.sumFuncTime("commonClearAA")
		if(typeof value == 'string' &&  value.indexOf("'") == 0 && value.lastIndexOf("'") == value.length - 1 ){
			return value.substring(1,value.length - 1)
		}else{
			return value
		}
	},
	/**
	 * 将字符串转换为数组，并处理其中的变量替换
	 * @param {string} s - 数组格式的字符串，如 "[item1,item2,item3]"
	 * @param {Map} mp - 变量映射表，用于替换数组中的变量
	 * @returns {Array|null} 转换后的数组，如果不是有效的数组格式则返回null
	 * @example
	 * xSysFunc.xToArray("[1,2,3]", null) // [1,2,3]
	 * xSysFunc.xToArray("[@@var1#type@@,item2]", variableMap) // 替换变量后的数组
	 */
	xToArray(s,mp){
		this.sumFuncTime("commonXToArray")
		if(this.checkArray(s)){
			let arr = s.trim().slice(1,-1).split(',')
			if(mp != null){
				for(let n=0;n<arr.length;n++){
					if(this.checkStrVar(arr[n])){
						arr[n] = this.doBackStr(arr[n],mp)
					}else if(this.checkObj(arr[n])){
						arr[n] = this.doBackObj(arr[n],mp)
					}
				}
			}
			return arr
		}
		return null
	},

	/**
	 * 显示警告提示消息
	 * @param {string} msg - 要显示的消息内容
	 * @example
	 * xSysFunc.showToast("操作成功")
	 */
	showToast(msg){
		ElMessage({
		    message: msg,
		    type: 'warning',
		  })
	},

	/**
	 * 显示对话框
	 * @param {string} msg - 对话框消息内容
	 * @param {string} title - 对话框标题
	 * @param {string} sureText - 确定按钮文本，为空时默认为"确定"
	 * @param {string} cancelText - 取消按钮文本，为空时不显示取消按钮
	 * @example
	 * xSysFunc.showDialog("确定要删除吗？", "提示", "确定", "取消")
	 * xSysFunc.showDialog("操作完成", "提示", "确定", "") // 只显示确定按钮
	 */
	showDialog(msg,title,sureText,cancelText){
		let showParam = {
			type: 'warning',
			center: true,
			confirmButtonText:sureText.length > 0 ? sureText : '确定',
		}
		if(cancelText && cancelText.length > 0 ){
			showParam.cancelButtonText = cancelText
			ElMessageBox.confirm(msg,title,showParam)
		}else{
			ElMessageBox.alert(msg,title,showParam)
		}
	},
	/**
	 * 字符串截取函数，安全处理边界情况
	 * @param {string} s - 要截取的字符串
	 * @param {number} b - 开始位置（包含）
	 * @param {number} e - 结束位置（不包含）
	 * @returns {string} 截取后的字符串
	 * @example
	 * xSysFunc.strSub("hello world", 0, 5) // "hello"
	 * xSysFunc.strSub("hello", 10, 15) // ""
	 */
	strSub(s,b,e){
		this.sumFuncTime("commonStrSub")
		if(s.length <= b){
			return ""
		}else if(s.length > b && s.length <= e){
			return s.substring(b)
		}else{
		  return s.substring(b,e)
		}
	},

	/**
	 * 打印函数，对象会转换为JSON字符串输出
	 * @param {any} s - 要打印的内容
	 * @example
	 * xSysFunc.xPrint("hello") // 输出: hello
	 * xSysFunc.xPrint({name: "test"}) // 输出: {"name":"test"}
	 */
	xPrint(s){
		console.log(typeof s=='object' ? JSON.stringify(s) : s)
	},

 /// --------- 上面是系统函数，下面是数据格式检查和处理函数 -----------------------------------------------

	/**
	 * 检查字符串是否为对象格式
	 * 对象格式: @#key1:value1,key2:value2#@
	 * @param {any} s - 要检查的值
	 * @returns {boolean} 如果是对象格式返回true
	 * @example
	 * xSysFunc.checkObj("@#name:张三,age:25#@") // true
	 * xSysFunc.checkObj("普通字符串") // false
	 */
	checkObj(s){
		this.sumFuncTime("commonCheckObj")
		return (typeof s == 'string' && s.trim().slice(0,2)=='@#'
		        && s.trim().slice(-2)=='#@' && s.trim().slice(2,-2).indexOf('#') > 0)
	},

	/**
	 * 检查字符串是否为变量格式
	 * 变量格式: @@variableName#type@@
	 * @param {any} s - 要检查的值
	 * @returns {boolean} 如果是变量格式返回true
	 * @example
	 * xSysFunc.checkStrVar("@@userName#string@@") // true
	 * xSysFunc.checkStrVar("普通字符串") // false
	 */
	checkStrVar(s){
		this.sumFuncTime("commonCheckStrVar")
		if(typeof s != 'string') return false
		let start = s.trim().indexOf("@@")
		let end = s.trim().slice(s.trim().indexOf("@@") + 2).indexOf('@@')
		return (start >=0 && end > 0 && s.trim().slice(start+2,start+2+end).indexOf('#') > 0)
	},

	/**
	 * 检查字符串是否为数组格式
	 * 数组格式: [item1,item2,item3]（但不包括[obj:]和[list:]开头的特殊格式）
	 * @param {any} s - 要检查的值
	 * @returns {boolean} 如果是数组格式返回true
	 * @example
	 * xSysFunc.checkArray("[1,2,3]") // true
	 * xSysFunc.checkArray("[obj:something]") // false
	 */
	checkArray(s){
		this.sumFuncTime("commonCheckArray")
		return (typeof s == 'string' && s.trim()[0]=='[' && s.trim().slice(-1)==']'
		    && s.indexOf('[obj:') != 0 && s.indexOf('[list:') != 0 )
	},
	/**
	 * 处理字符串中的变量替换
	 * 将字符串中的变量标记（@@variableName#type@@）替换为实际值
	 * @param {string} s - 包含变量标记的字符串
	 * @param {Map} mp - 变量映射表，key为变量标记，value为实际值
	 * @returns {string} 替换变量后的字符串
	 * @example
	 * // 假设mp中有 "@@name#string@@" -> "张三"
	 * xSysFunc.doBackStr("你好，@@name#string@@", mp) // "你好，张三"
	 */
	doBackStr(s,mp){
		this.sumFuncTime("commonDoBackStr")
		if(this.checkStrVar(s)){
			let mvar = s.split('@@')
			let tars = s
			for(let i=1;i<mvar.length;i++){
				if(mvar[i].trim()=='' || mvar[i].trim().indexOf('#') < 0) continue
				let key = '@@'+mvar[i]+'@@'
				if(mp.has(key)){
					tars = tars.replaceAll(key,mp.get(key))
				}
			}
			return tars
		}
		return s
	},
	/**
	 * 处理对象中的变量替换
	 * 支持两种对象格式：
	 * 1. [obj:variableName] - 直接引用变量中的对象
	 * 2. @#key1:value1,key2:value2#@ - 内联对象定义
	 * @param {string} s - 对象格式的字符串
	 * @param {Map} mp - 变量映射表
	 * @returns {Object|string} 处理后的对象或原字符串
	 * @example
	 * // 格式1: [obj:userInfo] -> 从mp中获取userInfo对象
	 * // 格式2: @#name:@@userName#string@@,age:25#@ -> {name: "实际用户名", age: "25"}
	 */
	doBackObj(s,mp){
		this.sumFuncTime("commonDoBackObj")
		// 处理 [obj:variableName] 格式
		if(s.length > 6 && s.slice(0,5)=='[obj:' && s.slice(-1) == ']'){
			let mvar = s.slice(5,s.length - 1)
			if(mp.has(mvar)){
				let varObj = mp.get(mvar)
				if(typeof varObj=='object'){
					// 递归处理对象中的字符串属性，替换其中的变量
					for(let keyName in varObj){
						if(typeof varObj[keyName] == 'string'){
						   varObj[keyName] = this.doBackStr(varObj[keyName],mp)
						}
					}
				}
				return varObj
			}else{
				return s
			}
		}
		// 处理 @#key1:value1,key2:value2#@ 格式
		else if(this.checkObj(s)){
			if(mp.has(s)){
				let mss = mp.get(s).replaceAll('\t','').replaceAll('\n','').replaceAll(';','').split(',')
				let retObj = {}
				for(let k=0;k<mss.length;k++){
					let kv = mss[k].split(':')
					if(kv.length == 2){
						let colName = kv[1].trim()
						if(this.checkStrVar(colName)){
							// 值是变量，需要替换
							let v = this.doBackStr(colName,mp)
							retObj[kv[0].trim()] = v
						}else if(this.checkObj(colName)){
							// 值是对象，递归处理
							let v = this.doBackObj(colName,mp)
							retObj[kv[0].trim()] = v
						}else{
							// 值是普通字符串
							retObj[kv[0].trim()] = colName
						}
					}
				}
				return retObj
			}
		}
		return s
	},

	/// --------- 下面是表达式中的函数处理 -----------------------------------------------

	/**
	 * 匹配并执行表达式中的函数
	 * 支持多种内置函数：:sub()、:contain()、:indexOf()、:xPrint()、:length()、:showDialog()、:showToast()
	 * @param {string} s - 函数表达式字符串
	 * @param {Map} mp - 变量映射表
	 * @returns {any} 函数执行结果
	 * @example
	 * xSysFunc.matchMeth(":sub(hello,0,3)", mp) // "hel"
	 * xSysFunc.matchMeth(":contain(hello,ell)", mp) // true
	 */
	matchMeth(s,mp){
		this.sumFuncTime("commonMatchMeth")

		// 字符串截取函数 :sub(string, start, end) 或 :截取(string, start, end)
		if((s.substring(0,':sub('.length) == ':sub(' && s[s.length - 1]==')')  ||
		         (s.substring(0,':截取('.length) == ':截取(' && s[s.length - 1] == ')')){
			let p = this.getMathParam(s).split(',')
			if(p.length == 3 && this.isNumeric(p[1]) && this.isNumeric(p[2])){
				p[0] = this.doBackStr(p[0],mp)
				return this.strSub(p[0],p[1],p[2])
			}
			console.log( "函数："+s+" , 参数异常")
			return ""
		}

		// 包含检查函数 :contain(string1, string2) - 检查string1是否包含string2
		else if(s.substring(0,':contain('.length) == ':contain(' && s[s.length - 1] == ')'){
			let p = this.getMathParam(s).split(',')
			if(p.length == 2 ){
				if(p[0] != '' && p[0].slice(0,5) == '[obj:'){
					p[0] = this.doBackObj(p[0],mp)
				}else{
					p[0] = this.doBackStr(p[0],mp)
				}
				if(p[1] != '' && p[1].slice(0,5) == '[obj:'){
					p[1] = this.doBackObj(p[1],mp)
				}else{
					p[1] = this.doBackStr(p[1],mp)
				}
				return p[0].includes(p[1])
			}
			console.log( "函数："+s+" , 参数异常")
			return false
		}

		// 索引查找函数 :indexOf(string1, string2) - 查找string2在string1中的位置
		else if(s.substring(0,':indexOf('.length) == ':indexOf(' && s[s.length - 1] == ')'){
			let p = this.getMathParam(s).split(',')
			if(p.length == 2 ){
				p[0] = this.doBackStr(p[0],mp)
				p[1] = this.doBackStr(p[1],mp)
				return p[0].indexOf(p[1])
			}
			console.log( "函数："+s+" , 参数异常")
			return -1
		}

		// 打印函数 :xPrint(value) 或 :打印(value) - 在控制台输出值
		else if((s.substring(0,':xPrint('.length) == ':xPrint(' && s[s.length - 1] == ')' ) ||
		          (s.substring(0,':打印('.length) == ':打印(' && s[s.length - 1] == ')')){
			let p = this.getMathParam(s).split(',')
			if(p.length == 1 ){
				if(p[0] != '' && p[0].slice(0,5) == '[obj:'){
					return this.xPrint(this.doBackObj(p[0],mp))
				}else{
					return this.xPrint(this.doBackStr(p[0],mp))
				}
			}
			console.log( "函数："+s+" , 参数异常")
		}

		// 长度获取函数 :length(value) - 获取字符串或数组的长度
		else if(s.substring(0,':length('.length) == ':length(' && s[s.length - 1] == ')'){
			let p = this.getMathParam(s).split(',')
			if(p.length == 1 ){
				let col = p[0]
				if(col.slice(0,5) == '[obj:' && col.slice(-1)==']'){
					col = col.slice(5,col.length - 1)
				}
				if(col.length > 0){
					if(mp.has(col)){
						let xVal =mp.get(col)
						return xVal.length
					}
					return col.length
				}
			}
			console.log( "函数："+s+" , 参数异常")
			return 0
		}

		// 对话框显示函数 :showDialog(msg, title, confirmText, cancelText) 或 :弹窗(...)
		else if((s.substring(0,':showDialog('.length) == ':showDialog(' && s[s.length - 1] == ')') ||
		         (s.substring(0,':弹窗('.length) == ':弹窗(' && s[s.length - 1] == ')')){
			let p = this.getMathParam(s).split(',')
			if(p.length >= 1 ){
				let msg = p[0]
				let title = p.length > 1 ? p[1] : ""
				let confimText = p.length > 2 ? p[2] : ""
				let cancelText = p.length > 3 ? p[3] : ""

				this.showDialog(msg,title,confimText,cancelText)
				return 0
			}
			console.log( "函数："+s+" , 参数异常")
			return -1
		}

		// 提示消息显示函数 :showToast(msg) - 显示警告提示
		else if(s.substring(0,':showToast('.length) == ':showToast(' && s[s.length - 1] == ')'){
			let p = this.getMathParam(s).split(',')
			if(p.length == 1 ){
				if(p[0] != '' && p[0].slice(0,5) == '[obj:'){
					this.showToast(this.doBackObj(p[0],mp))
				}else{
					this.showToast(this.doBackStr(p[0],mp))
				}
				return 0
			}
			console.log( "函数："+s+" , 参数异常")
			return -1
		}

		// 未能识别的函数
		else{
			if(s.trim().slice(0)==":" && s.trim().indexOf('(') > 0){
				throw new Error("函数 "+s + " 未能识别")
			}
		}
		return ""
	},
	/**
	 * 从函数表达式中提取参数部分
	 * @param {string} s - 函数表达式，如 ":sub(hello,0,3)"
	 * @returns {string} 参数字符串，如 "hello,0,3"
	 * @example
	 * xSysFunc.getMathParam(":sub(hello,0,3)") // "hello,0,3"
	 */
	getMathParam(s){
		this.sumFuncTime("commonGetMathParam")
		let s1 = s.trim()
		if(s1.slice(0,1)==':' &&  s1.slice(-1) == ')'){
			return s1.substring(s1.indexOf('(') + 1,s1.length - 1)
		}
		return ""
	},

	/**
	 * 在字符串中查找函数表达式
	 * 查找以 : 开头的函数调用，但排除 ref: 和 item: 前缀
	 * @param {string} s - 要搜索的字符串
	 * @returns {string} 找到的完整函数表达式，如果没找到返回空字符串
	 * @example
	 * xSysFunc.findMeth("text :sub(hello,0,3) more") // ":sub(hello,0,3)"
	 */
	findMeth(s){
		this.sumFuncTime("commonFindMeth")
		let tps = s
		let funcName = ""
		let methPos = 0  // 函数开始位置（暂未使用）
		let tmpPos = 0   // 临时位置（暂未使用）

		// 基本格式检查：必须包含 : ( )
		if(s.indexOf(':') <0 || s.indexOf('(') < 0 || s.indexOf(')') < 0) return ""

		// 逐字符扫描，查找函数名
		for(let i=0;i<tps.length;i++){
			if(funcName.length == 0 && tps[i]==':'){
				// 排除 ref: 和 item: 前缀
				if((i > 3 && tps.substring(i - 3,i) == 'ref') || (i > 4 && tps.substring(i - 4,i) == 'item')){
					if(funcName.length > 0) {
						funcName = ""
						methPos = 0
						tmpPos = 0
					}
					continue
				}
				funcName = tps[i]
				methPos = i
				tmpPos = 0
			}else if("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_(".indexOf(tps[i]) >= 0
			          && funcName.length > 0){
				funcName += tps[i]
				if(tps[i] == '('){
					tmpPos = i
					break;
				}
			}else{
				funcName = ""
				methPos = 0
				tmpPos = 0
			}
		}
		// 如果找到了函数名，需要找到完整的函数表达式（包括参数和嵌套括号）
		if(funcName.length > 0){
			let lastS = tps.substring(tps.indexOf(funcName))
			let lastStr = lastS.substring(funcName.length)
			let restPos = 0
			let floor = 1  // 括号嵌套层级计数器

			// 处理嵌套括号，找到函数的结束位置
			for(let n=0;n<100;n++){
				let leftM = lastStr.indexOf('(')
				let rightM = lastStr.indexOf(')')

				if((leftM < 0 && rightM > 0) || (rightM > 0 && rightM < leftM)){
					// 找到右括号
					restPos += rightM+1
					floor--
					if(floor <= 0){
						// 括号匹配完成
						break;
					}else{
						lastStr = lastStr.substring(rightM+1)
						continue;
					}
				}else if(leftM >= 0 && leftM < rightM){
					// 找到左括号，进入更深层级
					lastStr = lastStr.substring(leftM+1)
					restPos += leftM+1
					floor++
					continue
				}else{
					break
				}
			}
			funcName = lastS.substring(0,funcName.length + restPos)
		}

		return funcName
	},
	
	/// --------- 下面是对象属性操作函数 -----------------------------------------------

	/**
	 * 获取对象的属性值，支持深层路径访问
	 * @param {Object|Map} xObj - 目标对象或Map
	 * @param {string} xColumn - 属性路径，支持点分隔的深层访问，如 "user.profile.name"
	 * @returns {any} 属性值，如果路径不存在返回null
	 * @example
	 * xSysFunc.getItem({user: {name: "张三"}}, "user.name") // "张三"
	 * xSysFunc.getItem(new Map([["key", "value"]]), "key") // "value"
	 */
	getItem(xObj,xColumn){
		this.sumFuncTime("commonGetItem")
		if(xColumn.indexOf('.') > 0){
			let fColumn = xColumn.split(".")[0]
			let nColumn = xColumn.substring(xColumn.indexOf('.')+1)
			if(xObj.hasOwnProperty(fColumn)){
				return this.getItem(xObj[fColumn],nColumn)
			}else{
				return null
			}
		}else{
			if(xObj instanceof Map){
				return xObj.get(xColumn)
			}else{
				return xObj[xColumn]
			}
		}
	},

	/**
	 * 设置对象的属性值，支持深层路径访问
	 * @param {Object|Map} xObj - 目标对象或Map
	 * @param {string} xColumn - 属性路径，支持点分隔的深层访问
	 * @param {any} xValue - 要设置的值
	 * @example
	 * xSysFunc.itemSet(obj, "user.name", "李四") // 设置 obj.user.name = "李四"
	 */
	itemSet(xObj,xColumn,xValue){
		this.sumFuncTime("commonItemSet")
		if(xColumn.indexOf('.') > 0){
			let fColumn = xColumn.split(".")[0]
			let nColumn = xColumn.substring(xColumn.indexOf('.')+1)
			this.itemSet(xObj[fColumn],nColumn,xValue)
		}else{
			if(xObj instanceof Map){
				xObj.set(xColumn,xValue)
			}else{
				xObj[xColumn] = xValue
			}
		}
	},

	/**
	 * 检查对象是否包含指定属性，支持深层路径访问
	 * @param {Object|Map} xObj - 目标对象或Map
	 * @param {string} xColumn - 属性路径，支持点分隔的深层访问
	 * @returns {boolean} 如果属性存在返回true，否则返回false
	 * @example
	 * xSysFunc.hasItem({user: {name: "张三"}}, "user.name") // true
	 * xSysFunc.hasItem({user: {}}, "user.age") // false
	 */
	hasItem(xObj,xColumn){
		this.sumFuncTime("commonHasItem")
		if(xColumn.indexOf('.') > 0){
			let fColumn = xColumn.split(".")[0]
			let nColumn = xColumn.substring(xColumn.indexOf('.')+1)
			let hs = xObj.hasOwnProperty(fColumn)
			if(xObj instanceof Map) hs = xObj.has(fColumn)
			return hs ? this.hasItem(xObj[fColumn],nColumn) : false
		}else{
			let hs = xObj.hasOwnProperty(xColumn)
			if(xObj instanceof Map) hs = xObj.has(xColumn)
			return hs
		}
	},
	/// --------- 下面是DOM操作函数 -----------------------------------------------

	/**
	 * 设置DOM组件的子元素
	 * 用于动态添加或替换页面组件的子组件
	 * @param {Object|Array} xObj - 当前页面所有组件的数据
	 * @param {string} xChild - box的名称，例如"[Tbox2].children"
	 * @param {Array} children - 要添加的子组件数据数组
	 * @param {string} mode - 添加模式："append"(追加) 或 "replace"(替换)
	 * @param {Object} PageRef - 页面引用对象，用于访问PageComponents
	 * @example
	 * // 替换模式：清空原有子组件，添加新的
	 * xSysFunc.domSet(pageData, "[Tbox2].children", newChildren, "replace", PageRef)
	 * // 追加模式：在原有子组件基础上添加新的
	 * xSysFunc.domSet(pageData, "[Tbox2].children", newChildren, "append", PageRef)
	 */
	domSet(xObj,xChild,children,mode,PageRef){
		this.sumFuncTime("commonDomSet")

		// 处理深层路径，如 "[Tbox2].children"
		if(xChild.indexOf('.') > 0){
			let fColumn = xChild.split(".")[0].replace(/^\[|\]$/g, "") // 去掉方括号
			let nColumn = xChild.substring(xChild.indexOf('.')+1)
			xObj = xObj.find(el => el.name === fColumn); // 查找对应的组件
			if (!xObj) return;

			// 递归处理剩余路径
			this.domSet(xObj,nColumn,children,mode,PageRef)
		}else{
			// 直接操作children属性
			if(xChild=='children'){
				// 确保children属性存在
				if(!xObj.hasOwnProperty('children')){
					xObj['children'] = []
				}

				if(mode=='replace'){
					// 替换模式：先记录第一个元素的id，然后在PageComponents中删除对应组件
					if(xObj[xChild].length > 0 && xObj[xChild][0] && xObj[xChild][0].id){
						let firstElementId = xObj[xChild][0].id;
						// 在页面的PageComponents下查找并删除该id对象
						if(PageRef && PageRef.PageComponents && PageRef.PageComponents[firstElementId]){
							delete PageRef.PageComponents[firstElementId];
							console.log('已从PageComponents中删除组件:', firstElementId);
						}
					}
					// 清空原有子组件
					xObj[xChild].splice(0, xObj[xChild].length)
				}

				// 添加新的子组件
				if(typeof children == 'object' && children !== null){
					
					xObj[xChild].push(children)
				}
			}
			// 操作指定组件的children属性
			else if(xObj[xChild] && xObj[xChild].hasOwnProperty('children')){
				if(mode=='replace'){
					// 替换模式：先记录第一个元素的id，然后在PageComponents中删除对应组件
					if(xObj[xChild].children.length > 0 && xObj[xChild].children[0] && xObj[xChild].children[0].id){
						let firstElementId = xObj[xChild].children[0].id;
						// 在页面的PageComponents下查找并删除该id对象
						if(PageRef && PageRef.PageComponents && PageRef.PageComponents[firstElementId]){
							delete PageRef.PageComponents[firstElementId];
							console.log('已从PageComponents中删除组件:', firstElementId);
						}
					}
					xObj[xChild].children.splice(0, xObj[xChild].children.length)
				}
				if(typeof children == 'object' && children !== null){
					xObj[xChild].push(children)
				}
			}
		}
	},

	/// --------- 下面是性能统计函数（目前已禁用） -----------------------------------------------

	/**
	 * 函数调用次数统计（目前已禁用）
	 * 用于统计各个函数的调用频率，便于性能分析和优化
	 * @param {string} funcName - 函数名称，用于标识统计的函数
	 * @example
	 * xSysFunc.sumFuncTime("commonIsNumeric") // 统计 isNumeric 函数调用次数
	 */
	sumFuncTime(funcName){
		// 性能统计代码已注释，如需启用可取消注释
		// if(!getApp().globalData.shareData.hasOwnProperty('sumObj')){
		// 	getApp().globalData.shareData['sumObj'] = {}
		// }
		// if(!getApp().globalData.shareData['sumObj'].hasOwnProperty(funcName)){
		// 	getApp().globalData.shareData['sumObj'][funcName] = 1
		// }else{
		// 	getApp().globalData.shareData['sumObj'][funcName] ++
		// }
		this.sumFuncTime2(funcName,1)
	},

	/**
	 * 函数调用次数统计（带数量参数，目前已禁用）
	 * @param {string} funcName - 函数名称（参数暂未使用）
	 * @param {number} num - 调用次数（参数暂未使用）
	 */
	sumFuncTime2(funcName,num){
		// 性能统计代码已注释，如需启用可取消注释
		// if(!getApp().globalData.shareData.hasOwnProperty('sumObj')){
		// 	getApp().globalData.shareData['sumObj'] = {}
		// }
		// if(!getApp().globalData.shareData['sumObj'].hasOwnProperty(funcName)){
		// 	getApp().globalData.shareData['sumObj'][funcName] = num
		// }else{
		// 	getApp().globalData.shareData['sumObj'][funcName] += num
		// }
	},

	/**
	 * 打印函数调用统计列表（目前已禁用）
	 * 输出所有函数的调用次数统计信息
	 */
	printSumFuncList(){
		// 性能统计代码已注释，如需启用可取消注释
		// let funcObj = getApp().globalData.shareData['sumObj']
		// for(let key in funcObj){
		// 	console.log("方法:"+key +" : "+ funcObj[key])
		// }
	}
	
}
