<template>
	<div :style="containerStyle" :class="currentFocus == 0 ? 'mTest ytest' : 'mTest ftest'">
	    <input 
			:type="xInputType" 
			:placeholder="xPlaceHolder" 
			v-model="xValue" 
			:style="inputStyle" 
			class="xtest"
			@focus="onFocus()" 
			@blur="onBlur()"
			:maxlength="xMaxLength"
		/>
		<img src="../../assets/circle-close.png" class="clear-test" v-if="xValue !=''" @click.stop="clearInputStr()" />
	</div>
</template>

<script lang="ts" setup>
	import { computed, ref, useAttrs, watch } from 'vue';
	import { useComponent } from '../../frame/useComponent.js'

	const props = useAttrs()
	const { xRefCss, xRefValue, xSetValue } = useComponent(props)

	// 获取others属性
	const others = computed(() => props.DomItem['others'] || {})

	// 从DomItem中获取的值，使用计算属性提高响应性
	let xValue = ref(xRefValue(props.DomItem['value']))
	const xPlaceHolder = computed(() => others.value['placeholder'] || '')
	const xInputType = computed(() => others.value['inputType'] || 'text')
	const xMaxLength = computed(() => others.value['maxlength'] || '100')

	// 焦点状态管理
	let currentFocus = ref(0)

	// 获取CSS属性
	const cssProps = computed(() => xRefCss(props.DomItem['css']) || {})

	// 计算容器样式：只保留位置和尺寸相关的属性
	const containerStyle = computed(() => {
		const allCss = cssProps.value
		return {
			position: allCss.position || 'relative',
			top: allCss.top,
			left: allCss.left,
			right: allCss.right,
			bottom: allCss.bottom,
			width: allCss.width,
			height: allCss.height,
			zIndex: allCss.zIndex
		}
	})

	// 计算输入框样式：包含除位置和尺寸之外的所有属性
	const inputStyle = computed(() => {
		const allCss = cssProps.value
		const result = {}

		// 复制所有非位置相关的CSS属性
		for (const key in allCss) {
			if (!['top', 'left', 'right', 'bottom', 'width', 'height', 'zIndex'].includes(key)) {
				result[key] = allCss[key]
			}
		}

		// 添加基本的输入框样式
		return {
			...result,
			boxSizing: 'border-box',
			width: '100%',
			height: '100%'
		}
	})

	// 处理输入框值变化，使用xSetValue修改原始位置的值
	function updateValue(newValue) {
		// 检查是否有有效的value引用路径
		console.log('props.DomItem[value]',props.DomItem['value'],newValue)
		if (props.DomItem && props.DomItem['value']) {
			xSetValue(props.DomItem['value'], newValue)
		}
	}

	// 监听xValue变化，实时更新原始位置的值
	watch(xValue, (newValue) => {
		updateValue(newValue)
	})

	// 清空输入框
	function clearInputStr() {
		xValue.value = ""
		// 清空时也要更新原始位置
		updateValue("")
	}

	function onFocus() {
		currentFocus.value = 1
	}

	function onBlur() {
		currentFocus.value = 0
		// 失焦时确保值已同步
		updateValue(xValue.value)
	}
</script>

<script lang="ts">
export default {
  name: 'FWInput'
}
</script>

<style lang="scss" scoped>
	.mTest {
		position: relative;
	}
	.ftest{
		background-color: var(--focus-background-color);
		color:var(--focus-color);
		transform: var(--focus-transform);
		font-size: var(--focus-font-size);
		border:var(--focus-border);
		border-radius: var(--focus-border-radius);
	}
	.clear-test{
		width:20px;
		height:20px;
		position: absolute;
		right:10px;
		top:8px;
		cursor:pointer;
	}
	.clear-test:active{
		transform: scale(0.94);
	}
	
	/* 隐藏number类型input的上下箭头 */
	input[type="number"]::-webkit-inner-spin-button,
	input[type="number"]::-webkit-outer-spin-button {
		-webkit-appearance: none;
		margin: 0;
	}
	
	/* Firefox */
	input[type="number"] {
		-moz-appearance: textfield;
		appearance: textfield;
	}
</style>