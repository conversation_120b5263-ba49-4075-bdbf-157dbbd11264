<template>
	<div class="fw-switch-container" :style="containerStyle" ref="containerRef">
		<div class="switch-core" 
			:class="{ 'is-checked': switchValue, 'is-disabled': !isEnabled }" 
			@click="toggleSwitch"
			:style="{ backgroundColor: switchValue ? activeColor : inactiveColor }">
			<div class="switch-button" ref="buttonRef"></div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { computed, ref, watch, useAttrs, onMounted, nextTick, toRaw } from 'vue';
	import { useComponent } from '../../frame/useComponent';
	
	interface DomItemType {
		// 使用更灵活的类型定义，以适应未来可能的变化
		css?: Record<string, any>;
		value?: any;
		checked?: any;
		enabled?: any;
		onChange?: any;
		id?: string | number;
		type?: string;
		PageEvents?: {
			onChange?: any;
			[key: string]: any;
		};
		[key: string]: any; // 允许任何其他属性
	}

	// 定义Props类型
	interface Props {
		DomItem?: DomItemType;
		[key: string]: any;
	}

	const props = useAttrs() as Props;
	const { xRefCss, xonChanged, xSetValue } = useComponent(props);
	
	// 移除计算属性中的console.log，直接获取原始DomItem对象
	const rawDomItem = toRaw(props.DomItem as DomItemType);
	
	// 只对checked属性使用响应式
	const checkedValue = computed(() => {
		const value = rawDomItem?.value;
		const checked = rawDomItem?.checked;
		
		if (value !== undefined) {
			return value === true || value === 'true';
		}
		
		if (checked !== undefined) {
			return checked === true || checked === 'true';
		}
		
		return false;
	});
	
	const containerRef = ref<HTMLElement | null>(null);
	const buttonRef = ref<HTMLElement | null>(null);
	
	// 获取开关的值，使用计算属性
	const initialValue = computed(() => checkedValue.value);
	
	// 监听值的变化
	const switchValue = ref(initialValue.value);
	
	watch(initialValue, (newValue) => {
		switchValue.value = newValue;
	});
	
	// 获取启用状态 - 不需要响应式
	const isEnabled = computed(() => {
		const enabled = rawDomItem?.enabled;
		if (enabled !== undefined) {
			return enabled === true || enabled === 'true';
		}
		return true;
	});
	
	// 获取颜色 - 不需要响应式
	const activeColor = computed(() => {
		return rawDomItem?.css?.selectColor?.activeColor || '#2b6bff';
	});
	
	const inactiveColor = computed(() => {
		return rawDomItem?.css?.selectColor?.disActiveColor || '#d0d0d0';
	});
	
	// 容器样式 - 处理位置和大小 - 不需要响应式
	const containerStyle = computed(() => {
		const css = rawDomItem?.css || {};
		return {
			position: css.position || 'absolute',
			top: css.top || '0px',
			left: css.left || '0px',
			width: css.width || '52px',
			height: css.height || '26px',
			zIndex: css.zIndex || 'auto'
		};
	});
	
	// 切换开关状态
	const toggleSwitch = () => {
		if (!isEnabled.value) return;
		
		const newValue = !switchValue.value;
		switchValue.value = newValue;
		handleChange(newValue);
	};
	
	// 处理变化
	const handleChange = (val: boolean) => {
		
		// 更新组件值
		// if (rawDomItem?.id) {
		// 	xSetValue(`ref:PageDom.[${rawDomItem.id}].value`, val);
		// 	xSetValue(`ref:PageDom.[${rawDomItem.id}].checked`, val);
		// }
		
		// 触发onChange事件
		if (props.DomItem?.PageEvents?.onChanged) {
			xonChanged(props.DomItem.PageEvents.onChanged);
		}
	};
	
	// 调整按钮大小，确保是正圆形
	const adjustButtonSize = () => {
		if (!containerRef.value || !buttonRef.value) return;
		
		const container = containerRef.value;
		const button = buttonRef.value;
		const height = container.offsetHeight;
		const width = container.offsetWidth;
		
		// 设置按钮直径为容器高度的90%
		const maxDiameter = Math.round(height * 0.9);
	
		button.style.width = `${maxDiameter}px`;
		button.style.height = `${maxDiameter}px`;
		
		// 计算左右边距
		const margin = Math.round((height - maxDiameter) / 2);
		button.style.margin = `0 ${margin}px`;
	};
	
	onMounted(() => {
		// 按钮大小调整
		adjustButtonSize();
	});
</script>

<script lang="ts">
export default {
  name: 'FWSwitch'
}
</script>

<style>
.fw-switch-container {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 椭圆背景 */
.switch-core {
  width: 100%;
  height: 100%;
  border-radius: 999px;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
  overflow: hidden;
}

.switch-core.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 按钮圆圈 */
.switch-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: white;
  transition: all 0.3s;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  left: 2px;
}

.switch-core.is-checked .switch-button {
  left: auto;
  right: 2px;
}
</style>
