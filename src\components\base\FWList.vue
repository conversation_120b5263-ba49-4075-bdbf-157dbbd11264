<template>
	<div :style="listStyle" class="list-outer-container" @click.stop="handleClick">
		<FWBox 
			:style="containerStyle"
			class="list-item-container"
			v-for="(xItem, index) in paginatedData"
			:key="index"
			:ItemDataPath="checkItemPath(props.DomItem['value'], startIndex + index)"
			:DomItem="props.DomItem['children'][0]"
		>		
			<!-- :ItemDataPath="checkItemPath(props.DomItem['value'], index)" -->
		</FWBox>

		<!-- Element Plus 分页组件 -->
		<div v-if="showPagination" :style="paginationStyle">
			<el-config-provider >
				<el-pagination
					v-model:current-page="currentPage"
					:page-size="pageSize"
					:total="filteredData.length"
					:background="true"
					layout="prev, pager, next"
					@current-change="handleCurrentChange"
					class="list-pagination"
				/>
			</el-config-provider>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive, inject, onMounted, useAttrs, computed, ref } from 'vue';
	import { useComponent  } from '../../frame/useComponent.js';
    import FWBox from './FWBox.vue';
	import { ElPagination, ElConfigProvider } from 'element-plus';
	import 'element-plus/es/components/pagination/style/css';
	import 'element-plus/es/components/config-provider/style/css';
	
	// 定义Props类型
	interface Props {
		DomItem?: {
			css?: Record<string, any>;
			children?: any[];
			value?: any;
			onClick?: any;
			type?: string;
			others?: {
				enablePagination?: boolean;
				numOfPage?: number;
				pageNoColor?: string;
				dataName?: string;
				paginationPosition?: boolean;
				[key: string]: any;
			};
			[key: string]: any;
		};
		PathPrefix?: string;
		ItemDataPath?: string;
	}
	
	const props = useAttrs() as Props;
	const { xRefCss, xOnClick, xRefValue, xSetValue ,xOnPageNoChange,xComponentObj} = useComponent(props);
	
	// 当前页码
	const currentPage = ref(1);

	// 计算列表子项
	const listChildren = computed(() => {
		return xRefValue(props.DomItem?.value);
	});

	// 过滤后的数据（去除空字符串等无效值）
	const filteredData = computed(() => {
		return listChildren.value.filter(item => {
			// 过滤掉空字符串、null、undefined
			return item !== '' && item !== null && item !== undefined;
		});
	});

	// 分页后的数据
	const paginatedData = computed(() => {
		if (!enablePagination.value) {
			return filteredData.value;
		}

		const start = startIndex.value;
		const end = start + pageSize.value;
		return filteredData.value.slice(start, end);
	});

	
	// 点击处理函数
	const handleClick = () => {
		if (props.DomItem?.onClick) {
			xOnClick(props.DomItem.onClick);
		}
	};
	
	// 页码变化处理
	const handleCurrentChange = (page: number) => {
		currentPage.value = page;
		if(props.DomItem?.PageEvents?.onPageNo){
			const pageEvent = JSON.parse(JSON.stringify(props.DomItem.PageEvents.onPageNo));
			pageEvent.params.pageNo = page;
			pageEvent.params.pageSize = pageSize.value;
			xOnPageNoChange(pageEvent);
		}

	};
	
	// 计算属性：列表样式（仅位置相关）
	const listStyle = computed(() => {
		const css = xRefCss(props.DomItem?.css) || {};
		
		// 提取位置相关的样式
		const positionStyles = {
			position: css.position || 'static',
			top: css.top,
			left: css.left,
			right: css.right,
			bottom: css.bottom,
			zIndex: css.zIndex,
			width: css.width
		};
		
		return positionStyles;
	});

	// 容器样式（非位置相关）
	const containerStyle = computed(() => {
		const css = xRefCss(props.DomItem?.css) || {};
		
		// 排除位置相关的属性
		const excludeProps = ['position', 'top', 'left', 'right', 'bottom', 'zIndex', 'width']; // 添加width到排除列表
		const otherStyles: Record<string, any> = {}; // 添加类型声明
		
		// 复制非位置相关的样式
		Object.keys(css).forEach(key => {
			if (!excludeProps.includes(key)) {
				otherStyles[key] = css[key];
			}
		});
		
		return otherStyles;
	});
	
	
	// 是否启用分页
	const enablePagination = computed(() => {
		return props.DomItem?.others?.enablePagination === true;
	});
	
	// 是否显示分页组件
	const showPagination = computed(() => {
		return enablePagination.value && listChildren.value.length > 0;
	});
	
	// 分页器位置样式
	const paginationStyle = computed(() => {
		// 检查是否需要粘附在底部
		const stickyBottom = props.DomItem?.others?.paginationPosition === true;
		// 获取列表的zIndex值
		const css = xRefCss(props.DomItem?.css) || {};
		const listZIndex = css['zIndex'] || 0;
		
		if (stickyBottom) {
			return {
				width: '100%',
				display: 'flex',
				justifyContent: 'center',
				padding: '15px 0',
				position: 'sticky',
				bottom: '0',
				zIndex: listZIndex
			};
		} else {
			return {
				width: '100%',
				display: 'flex',
				justifyContent: 'center',
				padding: '15px 0',
				marginTop: '10px'
			};
		}
	});
	
	// 每页数量
	const pageSize = computed(() => {
		const size = props.DomItem?.others?.numOfPage;
		return typeof size === 'number' && size > 0 ? size : 20;
	});
	
	// 分页颜色
	const pageNoColor = computed(() => {
		return props.DomItem?.others?.pageNoColor || '#2b6bff';
	});
	
	// 计算当前页的起始索引
	const startIndex = computed(() => {
		return enablePagination.value ? (currentPage.value - 1) * pageSize.value : 0;
	});
	


	

	function checkItemPath(listData:string,index:number){
		let datapath = "";
		

		if(typeof listData == 'string' && listData.substring(0,14) == 'ref:Component[' && listData.indexOf('].') > 0) {
			const cpObj = xComponentObj(listData)
			let key = cpObj.key
			
			cpObj.componentParams
			cpObj.componentVars
			if(cpObj.componentParams.length > 0) {
			
				cpObj.componentParams.forEach(e=>{
					if(e.name === key){
						// 页面或者全局的变量
						if (e.expression && e.expression.substring(0,4) === 'ref:'){
 							datapath = e.expression+"."+index;
						} else  {
							datapath = listData+"."+index;
						} 
					}
				})
			} else if(cpObj.componentVars.length > 0) {
				
				cpObj.componentVars.forEach(e=>{
				
					if(e.name === key){
					
						// 组件里面变量引用全局的
						if (e.expression && e.expression.startsWith('ref:GlobalData.')){
 							datapath = e.expression+"."+index;
						} else if (e.expression && e.expression.startsWith('ref:Component.')) {
							// 组件本身的变量
							datapath = listData+"."+index;
						} else {
							// 默认情况：组件变量直接包含数据
							datapath = listData+"."+index;
						}
						
					}
				})
			}
		} else if(typeof listData == 'string' && (listData.substring(0,13)=='ref:PageData.' || listData.startsWith('ref:GlobalData.'))){
			datapath = listData+"."+index
		}
		
		return datapath;
	}
</script>

<script lang="ts">
export default {
  name: 'FWList'
}
</script>

<style lang="scss" scoped>
.list-outer-container {
	overflow: auto;
	box-sizing: border-box;
	width: auto;
	height: inherit;
	*, *::before, *::after {
		box-sizing: border-box;
	}
	
	.list-item-container {
		width: 100%;
		height: inherit;
		position: relative;
		box-sizing: border-box; // 确保border包含在宽度内
		
		&.no-position-style {
			height: auto;
			position: static;
		}
	}
	
	.list-pagination {
		--el-color-primary: v-bind(pageNoColor);
	}
	
	/* 自定义滚动条 */
	&::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}
	
	/* 隐藏滚动条轨道 */
	&::-webkit-scrollbar-track {
		background: transparent;
	}
	
	/* 滚动条滑块 */
	&::-webkit-scrollbar-thumb {
		background: rgba(199, 201, 204, 0.5);
		border-radius: 3px;
	}
	
	/* 隐藏滚动条按钮 */
	&::-webkit-scrollbar-button {
		display: none;
	}
}
</style>
