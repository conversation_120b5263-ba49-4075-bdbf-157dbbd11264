<template>
	<el-icon 
		:style="iconStyle" 
		@click.stop="handleClick"
	>
		<component :is="resolvedIcon" />
	</el-icon>
</template>

<script lang="ts" setup>
	import { computed, useAttrs, onMounted } from 'vue';
	import { useComponent } from '../../frame/useComponent';
	import * as ElementPlusIconsVue from '@element-plus/icons-vue';
	
	// 定义Props类型
	interface Props {
		DomItem?: {
			css?: {
				iconPath?: string;
				iconColor?: string;
				iconSize?: string;
				[key: string]: any;
			};
			value?: string;
			onClick?: any;
			PageEvents?: {
				onClicked?: any;
			};
			[key: string]: any;
		};
	}

	const props = useAttrs() as Props;
	const { xRefCss, xOnClick, xCalc } = useComponent(props);

	const domItem = computed(() => props.DomItem);

	// 点击处理函数
	const handleClick = () => {
		if (props.DomItem?.PageEvents?.onClicked) {
			xOnClick(props.DomItem.PageEvents.onClicked);
		}
	};

	const iconName = computed(() => {
		const path = domItem.value?.css?.iconPath || 'Close';
		return xCalc(path); // 使用xCalc函数处理可能的表达式
	});
	
	const resolvedIcon = computed(() => {
		// 将iconName转换为Element Plus的图标名称格式
		const name = iconName.value;
		// 尝试直接匹配
		if (name in ElementPlusIconsVue) {
			return ElementPlusIconsVue[name as keyof typeof ElementPlusIconsVue];
		}
		// 尝试将首字母大写的方式匹配
		const pascalCase = name.charAt(0).toUpperCase() + name.slice(1);
		if (pascalCase in ElementPlusIconsVue) {
			return ElementPlusIconsVue[pascalCase as keyof typeof ElementPlusIconsVue];
		}
		// 默认返回Close图标（×符号）
		return ElementPlusIconsVue.Close;
		
	});
	
	const iconStyle = computed(() => {
		const css = xRefCss(domItem.value?.css);
		const iconColor = xCalc(domItem.value?.css?.iconColor);
		const iconSize = xCalc(domItem.value?.css?.iconSize);
		
		return {
			...css,
			color: iconColor || '#ff0000',
			fontSize: iconSize || '33px',
		};
	});
</script>

<script lang="ts">
export default {
  name: 'FWIcon'
}
</script>

<style lang="scss">
.el-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
</style>
