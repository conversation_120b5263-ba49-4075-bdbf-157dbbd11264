<template>
	<div :style="buttonStyle" @click="handleClick" @dblclick="handleDbClick" class="xtest">
	    {{ buttonValue }}
	</div>
</template>

<script lang="ts" setup>
	import { useAttrs, computed } from 'vue';
	import { useComponent } from '../../frame/useComponent.js'
	
	interface Props {
		DomItem?: {
			css?: Record<string, any>;
			value?: any;
			onClick?: any;
			[key: string]: any;
		}
	}

	const props = useAttrs() as Props;
	const { xRefCss, xOnClick, xRefValue, xOnDbClick } = useComponent(props)

	const buttonStyle = computed(() => xRefCss(props.DomItem?.css));

	const buttonValue = computed(() => xRefValue(props.DomItem?.value));

	const handleClick = () => {
		if (props.DomItem?.PageEvents?.onClicked) {
			xOnClick(props.DomItem.PageEvents.onClicked);
		}
	};
	
	const handleDbClick = () => {
		// Only call xOnDbClick if an onDbClick event is defined
		if (props.DomItem?.PageEvents?.onDbClicked) {
			xOnDbClick(props.DomItem.PageEvents.onDbClicked);
		}
		
	};
	
</script>

<script lang="ts">
export default {
  name: 'FWButton'
}
</script>

<style lang="scss">

</style>