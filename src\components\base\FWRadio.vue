<template>
	<div :style="containerStyle" class="radio-wrapper">
		<div
			v-for="item in dataList"
			:key="item.value"
			:class="['radio-wrapper', { checked: currentValue === item.value }]"
			@click="onSelect(item.value)"
			:style="radioItemStyle"
		>
			<input
				type="radio"
				:value="item.value"
				:checked="currentValue === item.value"
				:disabled="!isEnabled"
				class="radio-native"
			/>
			<span 
				:class="['radio-circle ', { checked: currentValue === item.value }]"
				:style="circleBaseStyle"
			></span> 
			<span
				:class="['radio-label']"
				:style="labelStyle"
			>{{ item.label }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { ref, watch, computed, useAttrs, onMounted } from 'vue';
	import { useComponent } from '../../frame/useComponent.js'
	
	const props = useAttrs() as any
	const { xRefCss, xRefValue, xSetValue } = useComponent(props)
	
	// CSS属性
	const cssProps = computed(() => {
		return xRefCss(props.DomItem['css']) || {}
	})
	
	// 处理数据源
	const dataList = computed(() => {	
		if (props.DomItem.choose && props.DomItem.choose.dataExpression) {
			const dynamicData = xRefValue(props.DomItem.choose.dataExpression.trim());
			if (dynamicData && Array.isArray(dynamicData)) {
				return dynamicData;
			}
		}
		
		// 回退到静态数据列表
		return props.DomItem.choose.dataList || [];
	})
	
	// 布局方向
	const layoutDirection = computed(() => {
		return props.DomItem.choose.direct || 'horizontal'
	})
	
	const isVerticalLayout = computed(() => {
		return layoutDirection.value === 'vertical'
	})
	
	// 最外层容器样式
	const containerStyle = computed(() => {
		return {
			width: cssProps.value['width'] || '100%',
			height: cssProps.value['height'] || 'auto',
			position: cssProps.value['position'] || 'static',
			top: cssProps.value['top'],
			left: cssProps.value['left'],
			right: cssProps.value['right'],
			bottom: cssProps.value['bottom'],
			zIndex: cssProps.value['zIndex'],
			overflow: 'hidden',
			display: 'flex',
			flexDirection: isVerticalLayout.value ? 'column' : 'row'
		}
	})
	
	// 内层radio-item样式
	const radioItemStyle = computed(() => {
		return {
			// 保留cssProps中的所有属性
			...cssProps.value,
			// 覆盖特定属性
			height: 'auto',
			position: 'relative',
			top: 'auto',
			left: 'auto',
			right: 'auto',
			bottom: 'auto',
			boxSizing: 'border-box',
			display: 'flex',
			alignItems: 'center',
			// 根据方向调整宽度和边距
			width: isVerticalLayout.value ? '100%' : 'auto',
		}
	})
	
	// 绑定当前选中值
	const currentValue = ref(props.DomItem.choose.currentValue)
	
	// 计算字体大小（基于CSS变量或默认值）
	const fontSize = computed(() => {
		const fontSizeValue = cssProps.value['--basic-font-size'] || cssProps.value['font-size'] || '16px';
		return parseInt(fontSizeValue);
	})
	
	// 计算圆圈大小
	const circleSize = computed(() => {
		return `${fontSize.value}px`;
	})
	
	// 计算圆点大小
	const dotSize = computed(() => {
		return `${fontSize.value * 0.7}px`;
	})
	
	// 获取文本颜色
	const textColor = computed(() => {
		return cssProps.value['--basic-color'] || cssProps.value['color'] || '#fff';
	})
	
	// 圆圈基础样式（不依赖于item的样式）
	const circleBaseStyle = computed(() => {
		return {
			position: 'absolute',
			left: '10px',
			top: '50%',
			transform: 'translateY(-50%)',
			width: circleSize.value,
			height: circleSize.value,
			borderRadius: '50%',
			border: `1px solid ${textColor.value}`,
			boxSizing: 'border-box',
			'--dot-size': dotSize.value,
			'--dot-color': textColor.value,
			transition: 'all 0.2s ease'
		}
	})
	
	// 筛选出所有与字体相关的属性
	const labelStyle = computed(() => {
		const fontStyles = {};
		
		// 遍历cssProps，筛选出所有字体和颜色相关属性，无论前缀如何
		Object.keys(cssProps.value).forEach(key => {
			// 保留所有字体相关属性
			if (key.includes('font') || key.includes('Font') || 
				key.includes('text') || key.includes('Text') ||
				key.includes('color') || key.includes('Color') ||
				key.includes('Color') || key.includes('decoration')) {
				fontStyles[key] = cssProps.value[key];
			}
			
			// 保留所有以--开头的CSS变量，可能包含字体相关设置
			if (key.startsWith('--')) {
				fontStyles[key] = cssProps.value[key];
			}
		});
		
		return fontStyles;
	})
	
	// 双向绑定到平台数据
	// watch(currentValue, (val) => {
	// 	xSetValue(props.DomItem['value'], val)
	// })
	
	// 控制禁用状态的计算属性
	const isEnabled = computed(() => {
		return xRefValue(props.DomItem['enabled'])
	})
	
	// 选择处理函数
	function onSelect(val: string) {
		if (isEnabled.value) {
			currentValue.value = val
		}
	}
</script>

<script lang="ts" >
export default {
	name: 'FWRadio'
}
</script>

<style lang="scss" scoped>
.radio-native {
	position: absolute;
	opacity: 0;
	pointer-events: none;
}

.radio-circle {
	position: relative;
	
	&.checked::after {
		content: '';
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: var(--dot-size);
		height: var(--dot-size);
		background: var(--dot-color);
		border-radius: 50%;
		transition: all 0.2s ease;
	}
}

.radio-label {
	flex: 1;
	line-height: 1.2;
	padding-left: 32px;
	color: var(--basic-color);
	font-size: var(--basic-font-size, inherit);
	font-weight: var(--basic-font-weight);
	font-family: var(--basic-font-family);
	text-decoration: var(--basic-text-decoration);
	text-align: var(--basic-text-align);
}

/* 外层容器样式 - 不包含悬停效果 */
.radio-wrapper {
	background-color: var(--basic-background-color);
	color: var(--basic-color);
	transform: var(--basic-transform);
	font-size: var(--basic-font-size);
	border: var(--basic-border);
	border-radius: var(--basic-border-radius);
	box-shadow: var(--basic-boxShadow);
	
	// 垂直布局时，最后一个子元素不需要底部边距
	&[style*="column"] > div:last-child {
		margin-bottom: 0 !important;
	}
	
	/* 内层单选框项样式 - 包含悬停效果 */
	> div.radio-wrapper {
		&:hover {
			background-color: var(--hover-background-color);
			color: var(--hover-color);
			transform: var(--hover-transform);
			font-size: var(--hover-font-size);
			border: var(--hover-border);
			border-radius: var(--hover-border-radius);
			box-shadow: var(--hover-boxShadow);
			
			// 在hover状态下改变内部label的样式
			.radio-label {
				color: var(--hover-color);
				font-size: var(--hover-font-size, inherit);
				font-weight: var(--hover-font-weight);
				font-family: var(--hover-font-family);
				text-decoration: var(--hover-text-decoration);
				text-align: var(--hover-text-align);
			}
		}
		
		&:active {
			background-color: var(--active-background-color);
			color: var(--active-color);
			transform: var(--active-transform);
			font-size: var(--active-font-size);
			border: var(--active-border);
			border-radius: var(--active-border-radius);
			box-shadow: var(--active-boxShadow);
			
			// 在active状态下改变内部label的样式
			.radio-label {
				color: var(--active-color);
				font-size: var(--active-font-size, inherit);
				font-weight: var(--active-font-weight);
				font-family: var(--active-font-family);
				text-decoration: var(--active-text-decoration);
				text-align: var(--active-text-align);
			}
		}
	}
}
</style>
