<template>
	<router-view></router-view>
</template>

<script lang="ts" setup >
	import {reactive,onMounted} from 'vue'
	import {useGlobalData} from '../pinia/globalData.js'
	import { getCurrentInstance } from 'vue';
	// import { useRouter } from 'vue-router';
	
	const { emitter } = getCurrentInstance()!.appContext.config.globalProperties;
	// const router = useRouter()
	// const globalData = useGlobalData()

	onMounted(()=>{
		// emitter.on("onYhy",()=>{
		// 	console.log("ok the emitter success")
		// })
		
		// router.push({
		// 	name:'Home',
		// })
		
		// postHomePage({
		// 	pageId:2344,
		// 	version:345
		// })
		// globalData.setGlobalData('user',{name:'ddddd'})
		
		// let kkk = globalData.getGlobalData('user').name
		// console.log("globalData.name = " + kkk)
		
		
	})
	
	// async function postHomePage(data:object){
	// 	let res = await homPage<PERSON>son(data)
	// 	console.log("result="+JSON.stringify(res))
	// }
	
	
	// onUnmounted(()=>{
	// 	emitter.off("onYhy")
	// })

</script>

<style lang="scss">
	
</style>