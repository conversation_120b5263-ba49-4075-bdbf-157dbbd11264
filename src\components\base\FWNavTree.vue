<template>
	<!-- <div :style="xRefCss(props.DomItem['css'])" @click="xOnClick(props.DomItem['onClick'])" class="xtest">
	    {{xRefValue(props.DomItem['value'])}}
	</div> -->
	<div :style="xRefCss(props.DomItem['css'])">
		<div v-for="(item,index) in listData" :key="index" class="top-box">
			<div class="top-bar" @click.stop="onTopBarClick(index)" 
			     :style="{'backgroundColor' : (item.expand || (currentCategoryId==item.id && currentItemId==0)) ? '#00a0a0' : '#303838'}">
				<img src="../../assets/big-item.png" class="bar-item-img"/>
				<div class="bar-label">{{item.name}}</div>
				<img v-show="item.children.length > 0" src="../../assets/arrow-left.png" :class="item.expand ? 'bar-img-expand' : 'bar-img'"/>
			</div>
			<div class="child-box" :style="{'height' : item.expand ? calcHeight(index) : '0px'}">
				<div v-for="(xItem,index2) in item.children" :key="index2" @click.stop="onItemClick(index,index2)"
				     :class="currentCategoryId==item.id && currentItemId==xItem.id ? 'child-label-select' : 'child-label'">
				   {{xItem.name}}
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { reactive,inject ,useAttrs,ref} from 'vue';
	import {useComponent} from '../../frame/useComponent.js'
	
	const props = useAttrs()
	const {xRefCss,xOnClick,xRefValue,xSetChildren} = useComponent(props)
	
	const PageRef = inject('PageRef')
	const listData = xRefValue(props.DomItem['listData'])
	
	let currentCategoryId = ref(0)
	let currentItemId = ref(0)
	
	function onTopBarClick(index:number){
		if(listData[index].children.length > 0){
			listData[index].expand = !listData[index].expand
		}else{
			currentCategoryId.value = listData[index].id
			currentItemId.value = 0
			xOpenPart(listData[index])
		}
		
	}
	
	function xOpenPart(target){
		if(target.hasOwnProperty('partId') && target.hasOwnProperty('parentId')){
			let targetStr = "ref:PageDom.["+target['parentId']+"].children"
			let param = target.hasOwnProperty('param') ? target['param'] : {}
			PageRef.loadPartJson(target['partId'],param,(res:object)=>{
				if(res.code==200){
					xSetChildren(targetStr,res.data.PageDom,'replace')
				}
			})
		}
	}

	function calcHeight(index:number){
		let n = listData[index].children.length
		if(n > 0){
		return (n*45 + 10) + 'px'
		}else{
			return '0px'
		}
	}
	
	function onItemClick(index:number,index2:number){
		currentCategoryId.value = listData[index].id
		currentItemId.value = listData[index].children[index2].id
		xOpenPart(listData[index].children[index2])
	}
	
</script>

<script lang="ts">
export default {
  name: 'FWNavTree'
}
</script>

<style lang="scss">
    .top-box{
		margin-right: 1px;
		cursor:pointer;
		
		.top-bar {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding:12px 12px;
			background-color: #303838;
			
			.bar-item-img{
				width:20px;
				height:20px;
			}
			
			.bar-label {
				margin-left: 10px;
				flex:1;
				color: white;
				font-size: 16px;
			}
			.bar-label:hover{
				color: #ffaa00;
			}
			.bar-img{
				width:20px;
				height:20px;
				transform: rotate(-90deg);
				transition: all 0.4s;
			}
			.bar-img-expand{
				width:20px;
				height:20px;
				transform: rotate(90deg);
				transition: all 0.4s;
			}
		}
		
		.child-box{
			width:inherit;
			transition: all 0.4s;
			overflow-y: hidden;
			background-color: rgb(16,176,176);
			
			.child-label-select {
				height:40px;
				line-height: 40px;
				text-align: left;
				font-size: 15px;
				padding-left: 60px;
				margin-top: 5px;
				color:#4c4c4c;
				background-color: #efefef;
			}
			
			.child-label{
				height:40px;
				line-height: 40px;
				text-align: left;
				font-size: 15px;
				padding-left: 60px;
				margin-top: 5px;
				color:white;
			}
			.child-label:hover{
                font-size: 17px;
			}
			.child-label:active{
				color: #d10000;
			}
		}
	}
</style>