<template>
	<div :style="lineStyle" class="xtest fw-line"></div>
</template>

<script lang="ts" setup>
	import { useAttrs, computed } from 'vue';
	import { useComponent } from '../../frame/useComponent.js'
	
	// 定义Props类型
	interface Props {
		DomItem?: {
			css?: Record<string, any>;
			[key: string]: any;
		};
	}
	
	const props = useAttrs() as Props;
	const { xRefCss } = useComponent(props);
	
	// 计算属性：线条样式
	const lineStyle = computed(() => xRefCss(props.DomItem?.css));
</script>

<script lang="ts">
export default {
  name: 'FWLine'
}
</script>

<style lang="scss">
	.fw-line {
		
	}
</style>
