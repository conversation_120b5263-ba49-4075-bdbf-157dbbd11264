import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      "/devApi": {
        target: "http://*************:8112",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/devApi/, ""),
      },
      "/proApi": {
        target: "http://localhost:8900",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/proApi/, ""),
      },
    },
  },
  define: {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
    __VUE_PROD_DEVTOOLS__: 'false',
    __VUE_OPTIONS_API__: 'true'
  }
})
