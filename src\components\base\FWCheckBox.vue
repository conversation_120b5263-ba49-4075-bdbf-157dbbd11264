<template>
	<div :style="containerStyle" class="checkbox-wrapper">
		<div
			v-for="item in dataList"
			:key="item.value"
			:class="['checkbox-wrapper', { checked: isChecked(item.value) }]"
			@click="onToggle(item.value)"
			:style="checkboxItemStyle"
		>
			<input
				type="checkbox"
				:value="item.value"
				:checked="isChecked(item.value)"
				:disabled="!xRefValue(props.DomItem['enabled'])"
				class="checkbox-native"
			/>
			<span 
				:class="['checkbox-box', { checked: isChecked(item.value) }]"
				:style="itemStyles[item.value]?.boxStyle"
			>
				<span 
					class="checkmark" 
					:style="itemStyles[item.value]?.checkmarkStyle"
				></span>
			</span> 
			<span
				:class="['checkbox-label']"
				:style="labelStyle"
			>{{ item.label }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
	import { ref, watch, useAttrs, computed } from 'vue';
	import { useComponent } from '../../frame/useComponent.js'
	
	const props = useAttrs() as any
	const { xRefCss, xRefValue, xSetValue } = useComponent(props)
	
	// CSS属性
	const cssProps = computed(() => xRefCss(props.DomItem?.css) || {});
	
	// 处理数据源
	const dataList = computed(() => {
		if (props.DomItem.choose?.dataExpression) {
			const dynamicData = xRefValue(props.DomItem.choose.dataExpression.trim());
			if (dynamicData && Array.isArray(dynamicData)) {
				return dynamicData;
			}
		}
		
		// 回退到静态数据列表
		return props.DomItem.choose?.dataList || [];
	})
	
	// 最外层容器样式
	const containerStyle = computed(() => {
		// 获取布局方向
		const direction = props.DomItem.choose?.direct || 'horizontal';
		const isVertical = direction === 'vertical';
		const allCss = cssProps.value;
		
		return {
			width: allCss['width'] || '100%',
			height: allCss['height'] || 'auto',
			position: allCss['position'] || 'static',
			top: allCss['top'],
			left: allCss['left'],
			right: allCss['right'],
			bottom: allCss['bottom'],
			zIndex: allCss['zIndex'],
			overflow: 'hidden',
			display: 'flex',
			flexDirection: isVertical ? 'column' : 'row'
		}
	});
	
	// 内层checkbox-item样式
	const checkboxItemStyle = computed(() => {
		// 获取布局方向
		const direction = props.DomItem.choose?.direct || 'horizontal';
		const isVertical = direction === 'vertical';
		
		return {
			// 保留cssProps中的所有属性
			...cssProps.value,
			// 覆盖特定属性
			height: 'auto',
			position: 'relative',
			top: 'auto',
			left: 'auto',
			right: 'auto',
			bottom: 'auto',
			boxSizing: 'border-box',
			display: 'flex',
			alignItems: 'center',
			// 根据方向调整宽度和边距
			width: isVertical ? '100%' : 'auto'
		}
	});
	
	// 绑定当前选中值（多选框是数组）
	const selectedValues = ref(props.DomItem.choose?.currentValue ? 
		props.DomItem.choose.currentValue.split(',').filter(Boolean) : []);
	
	// 检查是否选中
	function isChecked(value) {
		return selectedValues.value.includes(value);
	}

	// 字体大小计算属性
	const fontSize = computed(() => {
		const allCss = cssProps.value;
		const size = allCss['--basic-font-size'] || allCss['font-size'] || '16px';
		return parseInt(size);
	});

	// 为循环内元素计算样式
	const itemStyles = computed(() => {
		const styles: Record<string, { boxStyle: Record<string, any>, checkmarkStyle: Record<string, any> }> = {};
		const allCss = cssProps.value;
		const textColor = allCss['--basic-color'] || allCss['color'] || '#fff';
		const _boxSize = `${fontSize.value * 1.3}px`;
		const _checkmarkWidthSize = `${fontSize.value * 0.25}px`;
		const _checkmarkHeightSize = `${fontSize.value * 0.5}px`;

		for (const item of dataList.value) {
			const isItemChecked = selectedValues.value.includes(item.value);
			const checkmarkColor = isItemChecked ? '#fff' : textColor;

			styles[item.value] = {
				boxStyle: {
					position: 'absolute',
					left: '10px',
					top: '50%',
					transform: 'translateY(-50%)',
					width: _boxSize,
					height: _boxSize,
					borderRadius: '50%',
					border: `1px solid ${textColor}`,
					backgroundColor: isItemChecked ? textColor : 'transparent',
					boxSizing: 'border-box',
					transition: 'all 0.2s ease'
				},
				checkmarkStyle: {
					position: 'absolute',
					left: '50%',
					top: '50%',
					width: _checkmarkWidthSize,
					height: _checkmarkHeightSize,
					borderRight: `2px solid ${checkmarkColor}`,
					borderBottom: `2px solid ${checkmarkColor}`,
					transform: 'translate(-50%, -70%) rotate(45deg)',
					transition: 'all 0.2s ease',
					opacity: 1
				}
			};
		}
		return styles;
	});
	
	// 获取label样式
	const labelStyle = computed(() => {
		// 筛选出所有与字体相关的属性
		const fontStyles: Record<string, any> = {};
		const allCss = cssProps.value;
		
		// 遍历cssProps，筛选出所有字体和颜色相关属性，无论前缀如何
		Object.keys(allCss).forEach(key => {
			// 保留所有字体相关属性
			if (key.includes('font') || key.includes('Font') || 
				key.includes('text') || key.includes('Text') ||
				key.includes('color') || key.includes('Color') ||
				key.includes('Color') || key.includes('decoration')) {
				fontStyles[key] = allCss[key];
			}
			
			// 保留所有以--开头的CSS变量，可能包含字体相关设置
			if (key.startsWith('--')) {
				fontStyles[key] = allCss[key];
			}
		});
		
		return fontStyles;
	});
	
	// 双向绑定到平台数据
	watch(selectedValues, (val) => {
		xSetValue(props.DomItem['value'], val.join(','))
	})
	
	function onToggle(val: string) {
		if (!xRefValue(props.DomItem['enabled'])) return;
		
		const index = selectedValues.value.indexOf(val);
		if (index === -1) {
			// 添加值
			selectedValues.value.push(val);
		} else {
			// 移除值
			selectedValues.value.splice(index, 1);
		}
	}
</script>

<script lang="ts">
export default {
	name: 'FWCheckBox'
}
</script>

<style lang="scss" scoped>
.checkbox-native {
	position: absolute;
	opacity: 0;
	pointer-events: none;
}

.checkbox-box {
	position: relative;
}

.checkbox-label {
	flex: 1;
	line-height: 1.2;
	padding-left: 32px;
	color: var(--basic-color);
	font-size: var(--basic-font-size, inherit);
	font-weight: var(--basic-font-weight);
	font-family: var(--basic-font-family);
	text-decoration: var(--basic-text-decoration);
	text-align: var(--basic-text-align);
}

/* 外层容器样式 - 不包含悬停效果 */
.checkbox-wrapper {
	background-color: var(--basic-background-color);
	color: var(--basic-color);
	transform: var(--basic-transform);
	font-size: var(--basic-font-size);
	border: var(--basic-border);
	border-radius: var(--basic-border-radius);
	box-shadow: var(--basic-boxShadow);
	
	// 垂直布局时，最后一个子元素不需要底部边距
	&[style*="column"] > div:last-child {
		margin-bottom: 0 !important;
	}
	
	/* 内层复选框项样式 - 包含悬停效果 */
	> div.checkbox-wrapper {
		&:hover {
			background-color: var(--hover-background-color);
			color: var(--hover-color);
			transform: var(--hover-transform);
			font-size: var(--hover-font-size);
			border: var(--hover-border);
			border-radius: var(--hover-border-radius);
			box-shadow: var(--hover-boxShadow);
			
			// 在hover状态下改变内部label的样式
			.checkbox-label {
				color: var(--hover-color);
				font-size: var(--hover-font-size, inherit);
				font-weight: var(--hover-font-weight);
				font-family: var(--hover-font-family);
				text-decoration: var(--hover-text-decoration);
				text-align: var(--hover-text-align);
			}
		}
		
		&:active {
			background-color: var(--active-background-color);
			color: var(--active-color);
			transform: var(--active-transform);
			font-size: var(--active-font-size);
			border: var(--active-border);
			border-radius: var(--active-border-radius);
			box-shadow: var(--active-boxShadow);
			
			// 在active状态下改变内部label的样式
			.checkbox-label {
				color: var(--active-color);
				font-size: var(--active-font-size, inherit);
				font-weight: var(--active-font-weight);
				font-family: var(--active-font-family);
				text-decoration: var(--active-text-decoration);
				text-align: var(--active-text-align);
			}
		}
	}
}
</style>
