<script setup>
	import frameVue from './pages/frame.vue';
	import { useLocalStorage,useGlobalData} from './pinia/globalData';
	const global = useGlobalData()
	const storage = useLocalStorage()
	const t_session = storage.getItem('session')
	if(t_session && t_session.length > 0){
		global.setSession(t_session)
	}
</script>

<template>
  <router-view></router-view>
</template>

<style lang="scss">
	* {
		box-sizing: border-box;
	}	
	body{
		margin: 0;
	}
   .xtest{
	   background-color: var(--basic-background-color);
	   color:var(--basic-color);
	   transform: var(--basic-transform);
	   font-size: var(--basic-font-size);
	   border:var(--basic-border);
	   border-radius: var(--basic-border-radius);
	   box-shadow: var(--basic-boxShadow);

   }
   .xtest:hover{
   	   background-color: var(--hover-background-color);
   	   color:var(--hover-color);
   	   transform: var(--hover-transform);
   	   font-size: var(--hover-font-size);
   	   border:var(--hover-border);
   	   border-radius: var(--hover-border-radius);
   	   box-shadow: var(--hover-boxShadow);
   }
   .xtest:active{
   	   background-color: var(--active-background-color);
   	   color:var(--active-color);
   	   transform: var(--active-transform);
   	   font-size: var(--active-font-size);
   	   border:var(--active-border);
   	   border-radius: var(--active-border-radius);
	   box-shadow: var(--active-boxShadow);
   }
   .ytest{
   	   background-color: var(--basic-background-color);
   	   color:var(--basic-color);
   	   transform: var(--basic-transform);
   	   font-size: var(--basic-font-size);
   	   border:var(--basic-border);
   	   border-radius: var(--basic-border-radius);
	   box-shadow: var(--basic-boxShadow);
   }
   .ytest:hover{
   	   background-color: var(--hover-background-color);
   	   color:var(--hover-color);
   	   transform: var(--hover-transform);
   	   font-size: var(--hover-font-size);
   	   border:var(--hover-border);
   	   border-radius: var(--hover-border-radius);
	   box-shadow: var(--hover-boxShadow);
   }
   .ztest{
   	   background-color: var(--basic-background-color);
   	   color:var(--basic-color);
   	   transform: var(--basic-transform);
   	   font-size: var(--basic-font-size);
   	   border:var(--basic-border);
   	   border-radius: var(--basic-border-radius);
	   box-shadow: var(--basic-boxShadow);
   }
   .ztest:active{
   	   background-color: var(--active-background-color);
   	   color:var(--active-color);
   	   transform: var(--active-transform);
   	   font-size: var(--active-font-size);
   	   border:var(--active-border);
   	   border-radius: var(--active-border-radius);
	   box-shadow: var(--active-boxShadow);
   }
</style>
