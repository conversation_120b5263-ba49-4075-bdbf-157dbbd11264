import http from "../netRequest.js";
import { useGlobalData } from '../../pinia/globalData';
const global = useGlobalData();

export const getHomePage = () => {
  return http.post(
    '/app/openApp',
    { isDebug: true },
    {
      headers: {
        appId: global.getAppId()
      }
    }
  );
};

export const getPage = (data)=>{
	return http.post(
		'/app/page',
		data,
		{
			headers: {
				authorization: global.getSession(),
				appId: global.getAppId()
			}
		}
	);
};

export const  getPart = (data)=>{
	return http.post(
		'/app/component',
		data,
		{
			headers: {
				authorization: global.getSession(),
				appId: global.getAppId()
			}
		}
	);
};

export const  getScript = (data)=>{
	return http.post('/app/event',data);
};

export const runServerJson = (data)=>{
	return http.post('/app/func',data);
};