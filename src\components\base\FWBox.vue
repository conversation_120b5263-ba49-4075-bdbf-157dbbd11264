<template>
	<div :style="boxStyle" :class="['xtest box', {'in-list': isInList}]" @click.stop="handleClick">
		<ComponentMgr 
			v-for="(xItem, index) in children" 
			:key="index" 
			:DomItem="xItem" 
			:PathPrefix="pathPrefix + '.children.' + index" 
			:ItemDataPath="props.ItemDataPath"
		/>
	</div>
</template>

<script lang="ts" setup>
	import { useAttrs, computed } from 'vue';
	import { useComponent } from '../../frame/useComponent.js';
    import ComponentMgr from '../ComponentMgr.vue';
	
	// 定义Props类型
	interface Props {
		DomItem?: {
			css?: Record<string, any>;
			children?: any[];
			onClick?: any;
			[key: string]: any;
		};
		PathPrefix?: string;
		ItemDataPath?: string;
	}
	
	const props = useAttrs() as Props;
	const { xRefCss, xOnClick, xRefValue } = useComponent(props);
	
	// 检查是否在列表环境中
	const isInList = computed(() => {
		return !!props.ItemDataPath;
	});
	
	// 样式
	const boxStyle = computed(() => {
		const css = xRefCss(props.DomItem?.css) || {};
		
		// 如果在列表中，修改定位方式为相对定位
		if (isInList.value) {
			return {
				...css,
				position: 'relative',
				top: '0px',
				left: '0px'
			};
		}
		
		return css;
	});
	
	// 子组件
	const children = computed(() => props.DomItem?.children || []);
	
	// 路径前缀
	const pathPrefix = computed(() => props.PathPrefix || '');
	
	const handleClick = () => {
		if (props.DomItem?.onClick) {
			xOnClick(props.DomItem.onClick);
		}
	};
	
</script>

<script lang="ts">
export default {
  name: 'FWBox'
}
</script>

<style lang="scss">
.xtest {
	&.in-list {
		position: relative !important;
		top: 0 !important;
		left: 0 !important;
		width: 100% !important;
	}
}
</style>