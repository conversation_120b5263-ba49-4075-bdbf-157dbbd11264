<template>
	<img 
		:style="[cssStyle, imgStyle]" 
		:src="imageSource" 
		@error="handleImageError"
		@click="handleClick" 
		class="xtest"
	/>
</template>

<script lang="ts" setup>
	import { reactive, inject, onMounted, useAttrs, computed, ref } from 'vue';
	import {useComponent} from '../../frame/useComponent.js'
	const props = useAttrs()
	const {xRefCss, xOnClick, xRefValue} = useComponent(props)
	
	interface DomItemType {
		css?: {
			iconPath?: string;
			iconColor?: string;
			iconSize?: string;
			[key: string]: any;
		};
		value?: string;
		onClick?: any;
		others?: {
			'object-fit'?: string;
			[key: string]: any;
		};
		[key: string]: any;
	}

	// 默认图片 - Base64格式的占位图片
	const defaultImageSrc = 'data:image/svg+xml;base64,' + 
		'PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcv' +
		'MjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiPjxwYXRoIGZpbGw9IiNlZWUiIGQ9Ik05' +
		'MjggMTYwSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJo' +
		'ODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDYz' +
		'MkgxMzZ2LTM5LjlsMTM4LjUtMTY0LjMgMTUwLjEgMTc4TDY1OC4xIDQ4OSA4ODggNzYxLjZWNzky' +
		'em0wLTEyOS44TDY2NC4yIDM5Ni44Yy0zLjItMy44LTktMy44LTEyLjIgMEw0MjQuNiA2NjYuNGwt' +
		'MTQ0LTE3MC43Yy0zLjItMy44LTktMy44LTEyLjIgMEwxMzYgNjUyLjdWMjMyaDc1MnY0MzAuMnoi' +
		'Lz48cGF0aCBmaWxsPSIjZWVlIiBkPSJNMzA0IDQ1NmM0OC42IDAgODgtMzkuNCA4OC04OHMtMzku' +
		'NC04OC04OC04OC04OCAzOS40LTg4IDg4IDM5LjQgODggODggODh6bTAtMTE2YzE1LjUgMCAyOCAx' +
		'Mi41IDI4IDI4cy0xMi41IDI4LTI4IDI4LTI4LTEyLjUtMjgtMjggMTIuNS0yOCAyOC0yOHoiLz48' +
		'L3N2Zz4=';

	// 安全地获取DomItem
	const domItem = computed(() => props.DomItem as DomItemType || {});

	// 计算CSS样式
	const cssStyle = computed(() => xRefCss(domItem.value.css));

	// 计算图片源
	const imageSource = computed(() => {
		const src = xRefValue(domItem.value.value);
		return src || defaultImageSrc;
	});

	// 计算图片样式
	const imgStyle = computed(() => {
		return {
			objectFit: domItem.value?.others?.['object-fit'] 
		};
	});

	// 图片加载错误处理
	const handleImageError = (e: Event) => {
		const target = e.target as HTMLImageElement;
		target.src = defaultImageSrc;
	};

	// 点击处理函数
	const handleClick = () => {
		if (domItem.value.PageEvents?.onClicked) {
			xOnClick(domItem.value.PageEvents.onClicked);
		}
	};
</script>

<script lang="ts">
export default {
  name: 'FWImage'
}
</script>

<style lang="scss">
</style>